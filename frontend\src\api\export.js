import client from './client'

export const exportApi = {
  // 导出项目数据为Excel
  async exportProjectExcel(projectId) {
    try {
      const response = await client.get(`/export/projects/${projectId}/excel`, {
        responseType: 'blob' // 重要：设置响应类型为blob
      })
      
      // 创建下载链接
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      // 从响应头获取文件名，如果没有则使用默认名称
      const contentDisposition = response.headers['content-disposition']
      let filename = `项目数据导出_${new Date().toISOString().slice(0, 10)}.xlsx`

      if (contentDisposition) {
        // 处理普通 filename="filename" 格式
        const normalMatch = contentDisposition.match(/filename=([^;]+)/)
        if (normalMatch) {
          filename = normalMatch[1].replace(/['"]/g, '') // 移除引号
        }
      }
      
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      return { success: true, filename }
    } catch (error) {
      console.error('导出Excel失败:', error)
      throw error
    }
  },

  // 获取项目统计数据
  async getProjectStatistics(projectId) {
    try {
      const response = await client.get(`/export/projects/${projectId}/statistics`)
      return response // 因为拦截器已经返回了response.data，所以这里直接返回response
    } catch (error) {
      console.error('获取统计数据失败:', error)
      throw error
    }
  }
}
