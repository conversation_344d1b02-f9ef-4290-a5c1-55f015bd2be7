import json
import io
from datetime import datetime
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils import get_column_letter

from app.db.database import get_db
from app.models.project import Project, Registration
from app.api.admin import get_current_admin

router = APIRouter()

@router.get("/projects/{project_id}/excel")
async def export_project_excel(
    project_id: int,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """导出项目登记数据为Excel文件"""
    
    # 获取项目信息
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )
    
    # 获取项目的所有登记数据
    registrations = db.query(Registration).filter(
        Registration.project_id == project_id
    ).order_by(Registration.created_at.desc()).all()
    
    # 解析表单配置
    form_config = json.loads(project.form_config) if project.form_config else {"fields": []}
    fields = form_config.get("fields", [])
    
    # 创建Excel工作簿
    wb = Workbook()
    ws = wb.active
    ws.title = "登记数据"
    
    # 设置标题样式
    title_font = Font(name='微软雅黑', size=14, bold=True, color='FFFFFF')
    title_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    title_alignment = Alignment(horizontal='center', vertical='center')
    
    # 设置表头
    headers = ['序号', '提交时间']
    for field in fields:
        headers.append(field['label'])
    
    # 写入表头
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = title_font
        cell.fill = title_fill
        cell.alignment = title_alignment
    
    # 写入数据
    for row_idx, registration in enumerate(registrations, 2):
        # 序号
        ws.cell(row=row_idx, column=1, value=row_idx - 1)
        
        # 提交时间
        submit_time = registration.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ws.cell(row=row_idx, column=2, value=submit_time)
        
        # 解析表单数据
        form_data = json.loads(registration.form_data) if registration.form_data else {}
        
        # 写入各字段数据
        for col_idx, field in enumerate(fields, 3):
            field_name = field['name']
            field_type = field['type']
            value = form_data.get(field_name, '')
            
            # 处理不同类型的字段值
            if field_type == 'select' and value:
                # 对于选择框，显示标签而不是值
                options = field.get('options', [])
                for option in options:
                    if option['value'] == value:
                        value = option['label']
                        break
            elif field_type == 'date' and value:
                # 格式化日期
                try:
                    if isinstance(value, str):
                        value = datetime.fromisoformat(value.replace('Z', '+00:00')).strftime('%Y-%m-%d')
                except:
                    pass
            
            ws.cell(row=row_idx, column=col_idx, value=str(value) if value else '')
    
    # 调整列宽
    for col in range(1, len(headers) + 1):
        column_letter = get_column_letter(col)
        ws.column_dimensions[column_letter].width = 15
    
    # 添加项目信息工作表
    info_ws = wb.create_sheet("项目信息")
    info_data = [
        ['项目名称', project.title],
        ['项目描述', project.description or ''],
        ['分享链接', project.share_link],
        ['创建时间', project.created_at.strftime('%Y-%m-%d %H:%M:%S')],
        ['登记总数', len(registrations)],
        ['导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')]
    ]
    
    for row_idx, (label, value) in enumerate(info_data, 1):
        info_ws.cell(row=row_idx, column=1, value=label).font = Font(bold=True)
        info_ws.cell(row=row_idx, column=2, value=value)
    
    info_ws.column_dimensions['A'].width = 12
    info_ws.column_dimensions['B'].width = 30
    
    # 保存到内存
    excel_buffer = io.BytesIO()
    wb.save(excel_buffer)
    excel_buffer.seek(0)
    
    # 生成文件名（避免中文字符，使用英文文件名）
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"project_{project.id}_data_{timestamp}.xlsx"

    # 返回文件流
    return StreamingResponse(
        io.BytesIO(excel_buffer.read()),
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

@router.get("/projects/{project_id}/statistics")
async def get_project_statistics(
    project_id: int,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """获取项目统计数据"""
    
    # 获取项目信息
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )
    
    # 获取登记数据
    registrations = db.query(Registration).filter(
        Registration.project_id == project_id
    ).all()
    
    # 解析表单配置
    form_config = json.loads(project.form_config) if project.form_config else {"fields": []}
    fields = form_config.get("fields", [])
    
    # 基础统计
    total_count = len(registrations)
    
    # 按日期统计
    date_stats = {}
    for registration in registrations:
        date_key = registration.created_at.strftime('%Y-%m-%d')
        date_stats[date_key] = date_stats.get(date_key, 0) + 1
    
    # 字段统计
    field_stats = {}
    for field in fields:
        field_name = field['name']
        field_type = field['type']

        if field_type == 'select':
            # 统计选择框字段的选项分布
            options_count = {}

            for registration in registrations:
                form_data = json.loads(registration.form_data) if registration.form_data else {}
                value = form_data.get(field_name)
                if value:
                    # 找到对应的标签
                    label = value
                    for option in field.get('options', []):
                        if option['value'] == value:
                            label = option['label']
                            break
                    options_count[label] = options_count.get(label, 0) + 1

            field_stats[field['label']] = {
                'type': 'select',
                'data': options_count
            }
        else:
            # 统计其他类型字段的填写情况
            filled_count = 0
            empty_count = 0

            for registration in registrations:
                form_data = json.loads(registration.form_data) if registration.form_data else {}
                value = form_data.get(field_name)
                if value and str(value).strip():
                    filled_count += 1
                else:
                    empty_count += 1

            field_stats[field['label']] = {
                'type': 'fill_rate',
                'data': {
                    '已填写': filled_count,
                    '未填写': empty_count
                }
            }
    
    return {
        'project_info': {
            'id': project.id,
            'title': project.title,
            'description': project.description,
            'created_at': project.created_at.isoformat(),
            'share_link': project.share_link
        },
        'total_registrations': total_count,
        'date_statistics': date_stats,
        'field_statistics': field_stats
    }
